from feedgen.feed import FeedGenerator
from datetime import datetime, timezone, timedelta
import re

class RSSGenerator:
    def __init__(self, title, link, description):
        self.fg = FeedGenerator()
        self.fg.title(title)
        self.fg.link(href=link, rel='alternate')
        self.fg.id(link)
        self.fg.description(description)
        self.fg.author({'name': 'نظام الرصد الإعلامي', 'email': '<EMAIL>'})
        self.fg.language('ar')
        self.fg.lastBuildDate(datetime.now(timezone.utc))
        self.fg.generator('نظام الرصد الإعلامي v2.0')

    def parse_date(self, date_str):
        """تحليل التاريخ من النص العربي مع دعم محسن"""
        if not date_str:
            return datetime.now(timezone.utc)

        try:
            import re

            # تنظيف النص
            date_str = str(date_str).strip()

            # أنماط التاريخ المختلفة
            patterns = [
                r'(\d{4})-(\d{2})-(\d{2})',  # 2025-06-15
                r'(\d{2})/(\d{2})/(\d{4})',  # 15/06/2025
                r'(\d{2})-(\d{2})-(\d{4})',  # 15-06-2025
            ]

            for pattern in patterns:
                match = re.search(pattern, date_str)
                if match:
                    if pattern == patterns[0]:  # YYYY-MM-DD
                        year, month, day = int(match.group(1)), int(match.group(2)), int(match.group(3))
                    else:  # DD/MM/YYYY or DD-MM-YYYY
                        day, month, year = int(match.group(1)), int(match.group(2)), int(match.group(3))

                    # التحقق من صحة التاريخ
                    if 1 <= month <= 12 and 1 <= day <= 31 and 2020 <= year <= 2030:
                        return datetime(year, month, day, tzinfo=timezone.utc)

            # محاولة تحليل التواريخ النسبية
            today = datetime.now(timezone.utc)

            # التواريخ النسبية
            if any(word in date_str for word in ['اليوم', 'الآن', 'منذ دقائق', 'منذ ساعة', 'قبل قليل']):
                return today
            elif any(word in date_str for word in ['أمس', 'البارحة']):
                return today - timedelta(days=1)
            elif any(word in date_str for word in ['منذ يومين', 'قبل يومين']):
                return today - timedelta(days=2)

            # محاولة استخراج عدد الأيام
            days_match = re.search(r'منذ (\d+) يوم|قبل (\d+) يوم', date_str)
            if days_match:
                days = int(days_match.group(1) or days_match.group(2))
                return today - timedelta(days=days)

            # إذا فشل التحليل، استخدم التاريخ الحالي
            return today

        except Exception as e:
            print(f"خطأ في تحليل التاريخ '{date_str}': {e}")
            return datetime.now(timezone.utc)

    def add_items(self, items):
        """إضافة الأخبار إلى RSS"""
        for i, item in enumerate(items):
            try:
                fe = self.fg.add_entry()

                # العنوان
                title = item.get('title', f'خبر رقم {i+1}').strip()
                fe.title(title)

                # الرابط
                link = item.get('link', '').strip()
                if link:
                    fe.link(href=link)
                    fe.id(link)
                else:
                    fe.id(f"item-{i}-{hash(title)}")

                # الوصف
                description = title  # استخدام العنوان كوصف
                if len(description) > 200:
                    description = description[:200] + "..."
                fe.description(description)

                # التاريخ
                pub_date = self.parse_date(item.get('date'))
                fe.pubDate(pub_date)

                # معلومات إضافية
                fe.author({'name': 'مصدر الخبر'})

            except Exception as e:
                print(f"خطأ في إضافة الخبر: {e}")
                continue

    def rss_str(self):
        """إرجاع RSS كنص"""
        return self.fg.rss_str(pretty=True)

    def write(self, filename):
        """كتابة RSS إلى ملف"""
        try:
            self.fg.rss_file(filename)
            print(f"تم حفظ RSS في: {filename}")
        except Exception as e:
            print(f"خطأ في حفظ RSS: {e}")
            raise
